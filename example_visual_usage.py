#!/usr/bin/env python3
"""
Example usage script for the visual PDF processing system.

This script demonstrates how to use the visual PDF processing functionality
to extract text and images from PDFs and analyze them with vision models.
"""

import os
from pathlib import Path
from kg_agents.visual_pdf_processor import VisualPDFProcessor
from kg_agents.vision_analyzer import VisionAnalyzer


def setup_example_environment():
    """Setup the example environment with folder structure."""
    print("Setting up example environment...")
    
    # Create folder structure
    folders = [
        "subprocess_raw_data/raw",
        "subprocess_raw_data/staging", 
        "subprocess_raw_data/visuals"
    ]
    
    for folder in folders:
        Path(folder).mkdir(parents=True, exist_ok=True)
        print(f"Created folder: {folder}")
    
    print("\nExample environment setup complete!")
    print("Place your PDF files in: subprocess_raw_data/raw/")


def example_basic_visual_processing():
    """Example of basic visual PDF processing without vision model."""
    print("\n" + "="*60)
    print("EXAMPLE: Basic Visual PDF Processing (No Vision Model)")
    print("="*60)
    
    # Initialize processor
    processor = VisualPDFProcessor()
    
    # Check for PDF files
    pdf_files = list(Path("subprocess_raw_data/raw").glob("*.pdf"))
    
    if not pdf_files:
        print("No PDF files found in subprocess_raw_data/raw/")
        print("Please add some PDF files to test the functionality.")
        return
    
    # Process first PDF without vision model
    pdf_file = pdf_files[0]
    print(f"Processing: {pdf_file.name}")
    
    try:
        result = processor.process_pdf_with_visuals(pdf_file.name, vision_analyzer=None)
        
        print(f"\nResults:")
        print(f"  Status: {result['status']}")
        print(f"  Chunks created: {result['chunks_created']}")
        print(f"  Output folder: {result['folder_path']}")
        
        if result['status'] == 'success':
            print(f"  Chunk files: {len(result['chunk_files'])} files created")
            
            # Show first chunk as example
            if result['chunk_files']:
                first_chunk_path = result['chunk_files'][0]
                print(f"\nExample chunk content from: {first_chunk_path}")
                
                import json
                with open(first_chunk_path, 'r', encoding='utf-8') as f:
                    chunk_data = json.load(f)
                
                print(f"  Chunk ID: {chunk_data['chunk_id']}")
                print(f"  Content Type: {chunk_data['content_type']}")
                print(f"  Text Preview: {chunk_data['text'][:200]}...")
                print(f"  Visual Citations: {len(chunk_data['visual_citations'])}")
        
    except Exception as e:
        print(f"Error processing PDF: {e}")


def example_vision_model_processing():
    """Example of visual PDF processing with vision model."""
    print("\n" + "="*60)
    print("EXAMPLE: Visual PDF Processing with Vision Model")
    print("="*60)
    
    # Check for OpenAI API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("OpenAI API key not found in environment variables.")
        print("Set OPENAI_API_KEY environment variable to use vision model.")
        print("Example: export OPENAI_API_KEY='your-api-key-here'")
        return
    
    # Initialize processor and vision analyzer
    processor = VisualPDFProcessor()
    
    try:
        vision_analyzer = VisionAnalyzer()
        print("Vision analyzer initialized successfully.")
    except Exception as e:
        print(f"Failed to initialize vision analyzer: {e}")
        return
    
    # Check for PDF files
    pdf_files = list(Path("subprocess_raw_data/raw").glob("*.pdf"))
    
    if not pdf_files:
        print("No PDF files found in subprocess_raw_data/raw/")
        print("Please add some PDF files to test the functionality.")
        return
    
    # Process first PDF with vision model
    pdf_file = pdf_files[0]
    print(f"Processing with vision model: {pdf_file.name}")
    
    try:
        result = processor.process_pdf_with_visuals(pdf_file.name, vision_analyzer)
        
        print(f"\nResults:")
        print(f"  Status: {result['status']}")
        print(f"  Chunks created: {result['chunks_created']}")
        print(f"  Output folder: {result['folder_path']}")
        
        if result['status'] == 'success':
            print(f"  Chunk files: {len(result['chunk_files'])} files created")
            
            # Show chunks with visual content
            import json
            visual_chunks = 0
            
            for chunk_file in result['chunk_files']:
                with open(chunk_file, 'r', encoding='utf-8') as f:
                    chunk_data = json.load(f)
                
                if chunk_data['content_type'] == 'text_with_visuals':
                    visual_chunks += 1
                    print(f"\nVisual chunk found: {chunk_data['chunk_id']}")
                    print(f"  Page: {chunk_data['page_number']}")
                    print(f"  Visual citations: {len(chunk_data['visual_citations'])}")
                    
                    for citation in chunk_data['visual_citations']:
                        print(f"    - {citation['ref_id']}: {citation['source_image_path']}")
                        if citation['description_from_vision_model']:
                            desc = citation['description_from_vision_model']
                            print(f"      Vision analysis: {desc[:100]}...")
            
            print(f"\nTotal chunks with visual content: {visual_chunks}")
        
    except Exception as e:
        print(f"Error processing PDF with vision model: {e}")


def example_test_vision_analyzer():
    """Test the vision analyzer independently."""
    print("\n" + "="*60)
    print("EXAMPLE: Testing Vision Analyzer")
    print("="*60)
    
    # Check for OpenAI API key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("OpenAI API key not found in environment variables.")
        print("Set OPENAI_API_KEY environment variable to test vision analyzer.")
        return
    
    try:
        vision_analyzer = VisionAnalyzer()
        print("Vision analyzer initialized successfully.")
        print(f"Model: {vision_analyzer.model}")
        
        # Look for any images in the visuals folder
        visuals_folder = Path("subprocess_raw_data/visuals")
        if visuals_folder.exists():
            image_files = list(visuals_folder.glob("*.png")) + list(visuals_folder.glob("*.jpg"))
            
            if image_files:
                test_image = image_files[0]
                print(f"\nTesting with image: {test_image}")
                
                result = vision_analyzer.analyze_image_from_file(str(test_image))
                
                print(f"Analysis result:")
                print(f"  Status: {result['status']}")
                if result['status'] == 'success':
                    print(f"  Analysis: {result['analysis'][:200]}...")
                    print(f"  Tokens used: {result.get('tokens_used', 'N/A')}")
                else:
                    print(f"  Error: {result['error']}")
            else:
                print("No image files found in subprocess_raw_data/visuals/")
                print("Process a PDF first to generate test images.")
        else:
            print("Visuals folder not found. Process a PDF first to generate test images.")
            
    except Exception as e:
        print(f"Error testing vision analyzer: {e}")


def main():
    """Main function to run examples."""
    print("Visual PDF Processing Examples")
    print("="*60)
    
    # Setup environment
    setup_example_environment()
    
    # Run examples
    example_basic_visual_processing()
    example_vision_model_processing()
    example_test_vision_analyzer()
    
    print("\n" + "="*60)
    print("Examples complete!")
    print("\nTo use the visual processing system:")
    print("1. Place PDF files in subprocess_raw_data/raw/")
    print("2. Set OPENAI_API_KEY environment variable")
    print("3. Run: python visual_main.py")


if __name__ == "__main__":
    main()
