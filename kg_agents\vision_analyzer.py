"""Vision model integration for analyzing extracted images from PDFs."""

import base64
import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from openai import OpenAI


class VisionAnalyzer:
    """Handles image analysis using OpenAI's vision model."""

    def __init__(self, api_key: Optional[str] = None, config_file: Optional[str] = None):
        """
        Initialize the vision analyzer.

        Args:
            api_key: OpenAI API key. If None, will try to load from config file or environment variable.
            config_file: Path to JSON config file containing API key. If None, looks for 'openai_config.json' in same folder.
        """
        self.api_key = self._get_api_key(api_key, config_file)
        if not self.api_key:
            raise ValueError(
                "OpenAI API key is required. Provide it via:\n"
                "1. api_key parameter\n"
                "2. JSON config file (openai_config.json in kg_agents folder)\n"
                "3. OPENAI_API_KEY environment variable"
            )

        self.client = OpenAI(api_key=self.api_key)
        self.model = "gpt-4o-mini"  # Using the correct model name for vision

        self.analysis_prompt = (
            "Analyze this image and provide a detailed, objective description of its contents. "
            "If it is a chart or graph, extract the title, axes, and key data points. "
            "If it contains text, transcribe the key textual information. "
            "If it's a diagram or illustration, describe the main components and their relationships. "
            "Be specific and factual in your description."
        )

    def _get_api_key(self, api_key: Optional[str], config_file: Optional[str]) -> Optional[str]:
        """
        Get API key from various sources in order of priority.

        Args:
            api_key: Direct API key parameter
            config_file: Path to config file

        Returns:
            API key string or None if not found
        """
        # 1. Direct parameter
        if api_key:
            return api_key

        # 2. JSON config file
        try:
            if config_file is None:
                # Default config file in same folder as this module
                current_dir = Path(__file__).parent
                config_file = current_dir / "openai_config.json"
            else:
                config_file = Path(config_file)

            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    return config_data.get('api_key') or config_data.get('openai_api_key')
        except Exception as e:
            print(f"Warning: Could not read config file {config_file}: {e}")

        # 3. Environment variable
        return os.getenv('OPENAI_API_KEY')

    @staticmethod
    def create_config_file(api_key: str, config_path: Optional[str] = None) -> str:
        """
        Create a JSON config file with the API key.

        Args:
            api_key: OpenAI API key to store
            config_path: Path where to create the config file. If None, creates in kg_agents folder.

        Returns:
            Path to the created config file
        """
        if config_path is None:
            current_dir = Path(__file__).parent
            config_path = current_dir / "openai_config.json"
        else:
            config_path = Path(config_path)

        config_data = {
            "api_key": api_key,
            "model": "gpt-4o-mini",
            "description": "OpenAI API configuration for vision analysis"
        }

        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2)

        print(f"Config file created: {config_path}")
        return str(config_path)
    
    def encode_image_to_base64(self, image_data: bytes) -> str:
        """
        Encode image data to base64 string.
        
        Args:
            image_data: Raw image data as bytes
            
        Returns:
            Base64 encoded image string
        """
        return base64.b64encode(image_data).decode('utf-8')
    
    def analyze_image(self, image_data: bytes, custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze an image using OpenAI's vision model.
        
        Args:
            image_data: Raw image data as bytes
            custom_prompt: Optional custom prompt for analysis
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            # Encode image to base64
            base64_image = self.encode_image_to_base64(image_data)
            
            # Use custom prompt if provided, otherwise use default
            prompt = custom_prompt or self.analysis_prompt
            
            # Make API call to OpenAI
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{base64_image}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000,
                temperature=0.1  # Low temperature for more consistent, factual responses
            )
            
            # Extract the analysis from the response
            analysis = response.choices[0].message.content
            
            return {
                "status": "success",
                "analysis": analysis,
                "model_used": self.model,
                "tokens_used": response.usage.total_tokens if response.usage else None
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "analysis": None
            }
    
    def analyze_image_from_file(self, image_path: str, custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze an image from a file path.
        
        Args:
            image_path: Path to the image file
            custom_prompt: Optional custom prompt for analysis
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            return self.analyze_image(image_data, custom_prompt)
            
        except Exception as e:
            return {
                "status": "error",
                "error": f"Failed to read image file: {str(e)}",
                "analysis": None
            }
    
    def batch_analyze_images(self, images_data: list, custom_prompt: Optional[str] = None) -> list:
        """
        Analyze multiple images in batch.
        
        Args:
            images_data: List of image data (bytes) or file paths
            custom_prompt: Optional custom prompt for analysis
            
        Returns:
            List of analysis results
        """
        results = []
        
        for i, image_item in enumerate(images_data):
            print(f"Analyzing image {i+1}/{len(images_data)}...")
            
            if isinstance(image_item, str):
                # Assume it's a file path
                result = self.analyze_image_from_file(image_item, custom_prompt)
            elif isinstance(image_item, bytes):
                # Assume it's raw image data
                result = self.analyze_image(image_item, custom_prompt)
            else:
                result = {
                    "status": "error",
                    "error": "Invalid image data type. Expected str (file path) or bytes (image data).",
                    "analysis": None
                }
            
            results.append(result)
        
        return results


# Example usage and testing function
def test_vision_analyzer():
    """Test function for the VisionAnalyzer class."""
    try:
        analyzer = VisionAnalyzer()
        print("VisionAnalyzer initialized successfully.")
        print(f"Using model: {analyzer.model}")
        return True
    except Exception as e:
        print(f"Failed to initialize VisionAnalyzer: {e}")
        print("\nTo set up API key, you can:")
        print("1. Create config file:")
        print("   VisionAnalyzer.create_config_file('your-api-key-here')")
        print("2. Set environment variable:")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        return False


def setup_config_example():
    """Example of how to set up the config file."""
    print("Example: Setting up OpenAI config file")
    print("="*40)

    # This is just an example - don't run with real API key
    example_key = "sk-your-actual-api-key-here"
    print(f"To create config file with your API key:")
    print(f"VisionAnalyzer.create_config_file('{example_key}')")
    print("\nThis will create 'openai_config.json' in the kg_agents folder.")


if __name__ == "__main__":
    test_vision_analyzer()
    print()
    setup_config_example()
