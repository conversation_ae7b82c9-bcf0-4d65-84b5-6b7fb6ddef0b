#!/usr/bin/env python3
"""
Main script for visual PDF document analysis with image extraction and vision model integration.

This script processes PDF files from the 'subprocess_raw_data/raw' folder, extracts both text and images,
creates visual chunks with image-text associations, and uses OpenAI's vision model to analyze images.

Usage:
    python visual_main.py                           # Process all PDFs in subprocess raw folder
    python visual_main.py --pdf filename.pdf       # Process specific PDF file
    python visual_main.py --no-vision              # Process without vision model analysis
    python visual_main.py --help                   # Show help
"""

import argparse
import sys
import os
from pathlib import Path
from kg_agents.visual_pdf_processor import VisualPDFProcessor
from kg_agents.vision_analyzer import VisionAnalyzer


def main():
    """Main function to handle visual PDF processing."""
    parser = argparse.ArgumentParser(
        description="Process PDF files with visual content extraction and analysis"
    )
    parser.add_argument(
        "--pdf",
        type=str,
        help="Specific PDF file to process (must be in subprocess_raw_data/raw folder)"
    )
    parser.add_argument(
        "--raw-folder",
        type=str,
        default="subprocess_raw_data/raw",
        help="Path to raw PDF folder (default: subprocess_raw_data/raw)"
    )
    parser.add_argument(
        "--staging-folder",
        type=str,
        default="subprocess_raw_data/staging",
        help="Path to staging folder for visual chunks (default: subprocess_raw_data/staging)"
    )
    parser.add_argument(
        "--no-vision",
        action="store_true",
        help="Skip vision model analysis of images"
    )
    parser.add_argument(
        "--openai-api-key",
        type=str,
        help="OpenAI API key (can also be set via OPENAI_API_KEY environment variable or JSON config file)"
    )
    parser.add_argument(
        "--config-file",
        type=str,
        help="Path to JSON config file containing OpenAI API key (default: kg_agents/openai_config.json)"
    )
    parser.add_argument(
        "--list-pdfs",
        action="store_true",
        help="List all PDF files in the subprocess raw folder"
    )
    parser.add_argument(
        "--setup-folders",
        action="store_true",
        help="Create the subprocess_raw_data folder structure"
    )
    
    args = parser.parse_args()
    
    # Setup folders if requested
    if args.setup_folders:
        setup_folder_structure(args.raw_folder, args.staging_folder)
        return
    
    # Initialize visual processor
    processor = VisualPDFProcessor(args.raw_folder, args.staging_folder)
    
    # List PDFs if requested
    if args.list_pdfs:
        pdf_files = [f for f in Path(args.raw_folder).iterdir() if f.suffix.lower() == '.pdf']
        if pdf_files:
            print("PDF files in subprocess raw folder:")
            for pdf_file in pdf_files:
                print(f"  - {pdf_file.name}")
        else:
            print("No PDF files found in subprocess raw folder.")
        return
    
    # Initialize vision analyzer if not skipped
    vision_analyzer = None
    if not args.no_vision:
        try:
            # Set API key if provided
            if args.openai_api_key:
                os.environ['OPENAI_API_KEY'] = args.openai_api_key

            # Initialize with config file if provided
            vision_analyzer = VisionAnalyzer(
                api_key=args.openai_api_key,
                config_file=args.config_file
            )
            print("Vision analyzer initialized successfully.")
            print("API key source: ", end="")
            if args.openai_api_key:
                print("command line parameter")
            elif args.config_file:
                print(f"config file: {args.config_file}")
            else:
                print("default config file or environment variable")
        except Exception as e:
            print(f"Warning: Could not initialize vision analyzer: {e}")
            print("Proceeding without vision model analysis...")
            vision_analyzer = None
    else:
        print("Skipping vision model analysis as requested.")
    
    try:
        if args.pdf:
            # Process specific PDF
            print(f"Processing specific PDF with visual analysis: {args.pdf}")
            result = processor.process_pdf_with_visuals(args.pdf, vision_analyzer)
            print_result(result)
        else:
            # Process all PDFs
            print("Processing all PDFs in subprocess raw folder with visual analysis...")
            results = processor.process_all_pdfs_with_visuals(vision_analyzer)
            
            if not results:
                print("No PDF files found to process.")
                return
            
            print(f"\nVisual Processing Summary:")
            print(f"{'='*60}")
            
            successful = 0
            failed = 0
            total_chunks = 0
            
            for result in results:
                print_result(result)
                if result["status"] == "success":
                    successful += 1
                    total_chunks += result["chunks_created"]
                else:
                    failed += 1
            
            print(f"\nOverall Results:")
            print(f"  Successfully processed: {successful} PDFs")
            print(f"  Failed: {failed} PDFs")
            print(f"  Total visual chunks created: {total_chunks}")
            print(f"  Vision model used: {'Yes' if vision_analyzer else 'No'}")
            
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


def setup_folder_structure(raw_folder: str, staging_folder: str):
    """Setup the subprocess_raw_data folder structure."""
    print("Setting up subprocess_raw_data folder structure...")
    
    folders_to_create = [
        raw_folder,
        staging_folder,
        "subprocess_raw_data/visuals"
    ]
    
    for folder in folders_to_create:
        Path(folder).mkdir(parents=True, exist_ok=True)
        print(f"  Created: {folder}")
    
    # Create a README file in the raw folder
    readme_content = """# Subprocess Raw Data

This folder contains the parallel visual PDF processing system.

## Structure:
- raw/: Place your PDF files here for visual processing
- staging/: Processed visual chunks will be stored here
- visuals/: Extracted images from PDFs will be saved here

## Usage:
Run `python visual_main.py` from the project root to process PDFs with visual content extraction.
"""
    
    readme_path = Path("subprocess_raw_data/README.md")
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"  Created: {readme_path}")
    print("\nFolder structure setup complete!")
    print(f"Place your PDF files in: {raw_folder}")


def print_result(result):
    """Print processing result in a formatted way."""
    print(f"\nPDF: {result['pdf_name']}")
    print(f"  Status: {result['status']}")
    
    if result['status'] == 'success':
        print(f"  Visual chunks created: {result['chunks_created']}")
        print(f"  Folder: {result['folder_path']}")
    elif result['status'] == 'error':
        print(f"  Error: {result.get('error', 'Unknown error')}")
    elif result['status'] == 'no_content':
        print(f"  Warning: No content could be extracted from this PDF")


if __name__ == "__main__":
    main()
